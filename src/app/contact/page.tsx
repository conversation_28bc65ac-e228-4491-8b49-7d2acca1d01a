'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import Footer from '../../components/Footer';

const ContactPage: React.FC = () => {
  const [formData, setFormData] = useState({
    businessName: '',
    fullName: '',
    email: '',
    phone: '',
    message: ''
  });
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Here you would add your form submission logic
    console.log('Form submitted:', formData);
    // You can integrate with your API or email service here
    alert('Merci pour votre message! Nous vous contacterons bientôt.');
  };

  return (
    <div className="min-h-screen bg-[#FFFAF0] text-[#330709] relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header with elements positioned at extreme edges */}
        <div className="flex justify-between items-center w-full pt-12 pb-16">
          <h1 className="text-4xl font-bold text-[#330709] font-saint-regus pl-0">
            NOUS CONTACTER
          </h1>
          <Image
            src="/images/footer/pepit-logo-marron.svg"
            alt="Pepit logo"
            width={190}
            height={40}
            className="h-auto pr-0"
          />
        </div>

        <main className="max-w-7xl mx-auto">
          <div className="bg-white rounded-lg overflow-hidden shadow-sm mb-0">
            {/* Question header */}
            <div className="bg-[#FFE569] py-8 px-10">
              <h2 className="text-3xl font-bold text-[#330709]">
                UNE QUESTION ?
              </h2>
            </div>
            
            {/* Contact form */}
            <form onSubmit={handleSubmit} className="p-16 space-y-10">
              <div>
                <input
                  type="text"
                  name="businessName"
                  value={formData.businessName}
                  onChange={handleChange}
                  placeholder="Nom du commerce (si vous êtes commerçant)"
                  className="w-full px-6 py-5 text-xl border border-gray-300 rounded focus:outline-none"
                />
              </div>
              
              <div>
                <input
                  type="text"
                  name="fullName"
                  value={formData.fullName}
                  onChange={handleChange}
                  placeholder="Prénom et nom"
                  className="w-full px-6 py-5 text-xl border border-gray-300 rounded focus:outline-none"
                  required
                />
              </div>
              
              <div>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="Email"
                  className="w-full px-6 py-5 text-xl border border-gray-300 rounded focus:outline-none"
                  required
                />
              </div>
              
              <div>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder="Téléphone"
                  className="w-full px-6 py-5 text-xl border border-gray-300 rounded focus:outline-none"
                />
              </div>
              
              <div>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  placeholder="Votre message"
                  rows={10}
                  className="w-full px-6 py-5 text-xl border border-gray-300 rounded focus:outline-none"
                  required
                ></textarea>
              </div>
              
              <div className="flex justify-center mt-12">
                <button
                  type="submit"
                  className="bg-[#330709] text-[#FFE569] py-4 px-24 rounded text-2xl font-bold uppercase"
                >
                  Envoyer
                </button>
              </div>
            </form>
          </div>
        </main>

        {/* Footer - directly against the form */}
        <Footer />
      </div>
      
      {/* Back button */}
      <div className="fixed top-4 left-4 z-20">
        <Link 
          href="/" 
          className="bg-[#330709] text-white px-4 py-2 rounded-full font-bold hover:bg-opacity-90 transition-colors text-sm"
        >
          ← Retour
        </Link>
      </div>
    </div>
  );
};

export default ContactPage; 