'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import Footer from '../../components/Footer';

const EthicalCharterPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-[#FFFAF0] text-[#330709] relative overflow-hidden">
      {/* Right side decoration with specified SVG */}
      <div className="absolute right-0 top-0 h-full w-1/3 overflow-hidden">
        <Image
          src="/images/charte/charte_decorator.svg"
          alt="Decorative elements"
          fill
          className="object-cover object-left"
          priority
        />
      </div>

      {/* Main content container - using same max-w-7xl as in page.tsx */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header with Pepit logo */}
        <header className="flex justify-between items-center py-16">
          <h1 className="text-4xl font-bold text-[#330709] font-saint-regus">
            CHARTE ÉTHIQUE
          </h1>
          <Image
            src="/images/LOGO-COMPLET-BICOLORE.svg"
            alt="Pepit logo"
            width={120}
            height={40}
            className="h-auto"
          />
        </header>

        <main className="prose prose-lg max-w-none">
          <p className="mb-6 font-medium">
            Nous souhaitons mettre en avant une sélection d&apos;adresses de qualité où il fait bon manger, faisant ainsi
            rayonner ces commerçants locaux reconnus pour la qualité de leurs produits et savoir-faire.
          </p>
          
          <p className="mb-12 font-medium">
            Pour ce faire, Pepit a établi des critères qualitatifs et souples, se basant sur nos valeurs communes.
            Tous les commerçants partenaires Pepit ont signé la charte.
          </p>

          <section className="mb-10">
            <h2 className="text-2xl font-bold mb-4 text-[#330709] font-saint-regus">
              Qualité des produits
            </h2>
            <ul className="list-disc pl-6 space-y-2">
              <li>Des commerçants qui travaillent ou vendent des produits frais et de saison</li>
              <li>Des commerçants qui privilégient les fournisseurs locaux ou bio</li>
              <li>Les commerçants avec un savoir-faire artisanal ou qui préparent sur place</li>
            </ul>
          </section>

          <section className="mb-10">
            <h2 className="text-2xl font-bold mb-4 text-[#330709] font-saint-regus">
              Environnement
            </h2>
            <ul className="list-disc pl-6 space-y-2">
              <li>Des commerçants qui travaillent avec des producteurs locaux.</li>
              <li>Des commerçants qui adoptent des pratiques respectueuses de l&apos;environnement 
                  (agriculture responsable, réduction des emballages, réduction du gaspillage
                  alimentaire, gestion des déchets).</li>
            </ul>
          </section>

          <section className="mb-10">
            <h2 className="text-2xl font-bold mb-4 text-[#330709] font-saint-regus">
              Qualité du service
            </h2>
            <ul className="list-disc pl-6 space-y-2">
              <li>Une attitude accueillante et respectueuse de la clientèle</li>
              <li>Convivialité et conseils personnalisés</li>
              <li>Une hygiène irréprochable</li>
            </ul>
          </section>

          <p className="mb-12 font-medium">
            Ces critères incitatifs (au vu de la diversité des métiers et savoir-faire) sont revus annuellement en
            fonction des retours des commerçants et des utilisateurs. Par des indépendants et pour des
            indépendants, ces critères visent à inspirer et non exclure, créant ainsi une communauté rassemblée
            par des valeurs communes de qualité.
          </p>
        </main>

        {/* Footer - placed at the same level as in page.tsx */}
        <Footer />
      </div>
      
      {/* Back button */}
      <div className="fixed top-4 left-4 z-20">
        <Link 
          href="/" 
          className="bg-[#330709] text-white px-4 py-2 rounded-full font-bold hover:bg-opacity-90 transition-colors text-sm"
        >
          ← Retour
        </Link>
      </div>
    </div>
  );
};

export default EthicalCharterPage; 