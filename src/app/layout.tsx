// src/app/layout.tsx

import './globals.css';
import { ReactNode } from 'react';
import { lilitaOne, outfit } from '@/lib/fonts';

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="fr" className={`${lilitaOne.variable} ${outfit.variable}`}>
      <head>
        <title>Pepit</title>
        <meta name="description" content="Votre application de fidélité" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body>
        {children}
      </body>
    </html>
  );
}
