// src/app/page.tsx
'use client';

import React from 'react';
import Navigation from '../components/Navigation';
import HeroSection from '../components/HeroSection';
import MissionSection from '../components/MissionSection';
import CashbackStepsSection from '../components/CashbackStepsSection';
import EconomicModelSection from '../components/EconomicModelSection';
import TestimonialsSection from '../components/TestimonialsSection';
import CommunitySection from '../components/CommunitySection';
import MerchantBenefitsSection from '../components/MerchantBenefitsSection';
import DevelopmentSection from '../components/DevelopmentSection';
import Footer from '../components/Footer';

const PepitHomePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-beige overflow-hidden">
      <div className="w-full overflow-x-hidden">
        {/* Navigation */}
        <Navigation />

        {/* Add spacing for the next section */}
        <div className="pt-36"></div>

        <main>
          {/* Hero Section */}
          <HeroSection />

          {/* Mission Section */}
          <MissionSection />

          {/* Cashback Steps Section */}
          <CashbackStepsSection />

          {/* Economic Model Section */}
          <EconomicModelSection />

          {/* Testimonials Section */}
          <TestimonialsSection />

          {/* Community Section */}
          <CommunitySection />

          {/* Merchant Benefits Section */}
          <MerchantBenefitsSection />

          {/* Development Section */}
          <DevelopmentSection />
        </main>

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
};

export default PepitHomePage;