import React from "react";
import AppStoreButtons from "@/components/ui/AppStoreButtons";
import Image from "next/image";
import { lilitaOne, outfit } from "@/lib/fonts";
import { Button } from "@/components/ui/button";
import Link from "next/link";

// Main component
export default function Commercants(): JSX.Element {
  return (
    <div className={`bg-[#fffdf3] flex flex-col items-center w-full ${lilitaOne.variable} ${outfit.variable}`}>
      <div className="bg-[#fffdf3] w-full max-w-[1200px] min-h-screen relative px-4 py-8">
        {/* Logo and Navigation Area - Center logo with grid layout */}
        <div className="grid grid-cols-3 items-center mb-14">
          <div className="col-start-1"></div>
          <div className="col-start-2 flex justify-center">
            <Image
              width={150}
              height={40}
              className="w-[150px]"
              alt="Pepit text logo"
              src="/images/commercants/pepit-logo.svg"
            />
          </div>
          <div className="col-start-3 flex justify-end">
            <AppStoreButtons />
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex flex-row relative">
          {/* Left Content */}
          <div className="w-[60%] pr-10">
            {/* Hero Heading */}
            <h1 className="font-lilita text-[#320607] text-[38px] leading-[1.2] mb-8">
              REJOIGNEZ L&apos;APP DE FIDÉLITÉ<br />
              QUI SOUTIENT L&apos;ÉCONOMIE LOCALE<br />
              ET INDÉPENDANTE !
            </h1>

            {/* CTA Button */}
            <Link href="/commercants/inscription">
              <Button className="bg-[#fee566] rounded-full px-14 py-4 mb-14 hover:bg-[#fde04d]">
                <span className="font-lilita text-[#320606] text-2xl">
                  C&apos;EST PARTI
                </span>
              </Button>
            </Link>

            {/* Key Figures Section */}
            <h2 className="font-outfit font-bold text-[#320607] text-xl mb-5">
              Chiffres clés
            </h2>

            <div className="flex gap-8 mb-5">
              {/* Average Basket Card */}
              <div className="flex items-center">
                <div className="mr-3">
                  <Image
                    width={40}
                    height={40}
                    src="/images/commercants/basket-icon.svg"
                    alt="Basket icon"
                    className="w-[40px] h-[40px]"
                  />
                </div>
                <div>
                  <p className="font-outfit font-semibold text-[#070606]">
                    Panier moyen
                  </p>
                  <div className="w-[120px] h-6 bg-[#fee566] rounded-full mt-1 flex items-center justify-center">
                    <span className="font-bold text-black">10 à 20%</span>
                  </div>
                  <p className="font-outfit font-semibold text-[#070606] text-sm mt-1">
                    plus important
                  </p>
                </div>
              </div>

              {/* Purchase Frequency Card */}
              <div className="flex items-center">
                <div className="mr-3">
                  <Image
                    width={40}
                    height={40}
                    src="/images/commercants/wallet-icon.svg"
                    alt="Wallet icon"
                    className="w-[40px] h-[40px]"
                  />
                </div>
                <div>
                  <p className="font-outfit font-semibold text-[#070606]">
                    Fréquence d&apos;achat
                  </p>
                  <div className="w-[120px] h-6 bg-[#fee566] rounded-full mt-1 flex items-center justify-center">
                    <span className="font-bold text-black">10 à 20%</span>
                  </div>
                  <p className="font-outfit font-semibold text-[#070606] text-sm mt-1">
                    plus important
                  </p>
                </div>
              </div>
            </div>

            {/* Footer Text */}
            <p className="font-outfit text-black">
              <span className="font-light">
                Efficacité moyenne d&apos;un programme de fidélité cashback
              </span>
              <span className="font-thin text-sm block mt-1">
                Etude du syndicat national du marketing à la performance 2019
              </span>
            </p>
          </div>

          {/* Device Mockups - Positioned absolutely */}
          <div className="absolute right-0 top-[-70px] w-[45%] h-[100%] overflow-visible">
            <Image
              width={550}
              height={550}
              className="absolute top-[0px] right-0 w-auto h-[550px]"
              alt="iPhone mockup"
              src="/images/commercants/iphone-mockup.svg"
            />
            <Image
              width={250}
              height={250}
              className="absolute top-[300px] right-[120px] w-auto h-[250px]"
              alt="MacBook mockup"
              src="/images/commercants/macbook-mockup.svg"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
