'use client';

import React, { useState } from "react";
import Image from "next/image";
import { lilitaOne, outfit } from "@/lib/fonts";
import { Input } from "@/components/ui/input";
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';
import "./select-placeholder.css";
import "./phone-input.css";

export default function CommercantInscription(): JSX.Element {
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    email: '',
  });

  const [phoneNumber, setPhoneNumber] = useState<string>('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission - for now just log the data
    console.log('Form submitted:', { ...formData, phone: phoneNumber });
    // Navigate to next step would go here
  };

  return (
    <div className={`bg-[#fffdf3] flex flex-col items-center w-full min-h-screen ${lilitaOne.variable} ${outfit.variable}`}>
      <div className="bg-[#fffdf3] w-full max-w-[1200px] min-h-screen flex flex-col md:flex-row px-4 md:px-0 pt-20 pb-10 md:py-0">
        {/* Logo at the top */}
        <div className="absolute top-8 left-1/2 transform -translate-x-1/2 z-10">
          <Image
            width={150}
            height={40}
            className="w-[120px] md:w-[150px]"
            alt="Pepit text logo"
            src="/images/commercants/pepit-logo.svg"
          />
        </div>

        {/* Left side - Phone mockup (hidden on mobile) */}
        <div className="hidden md:flex w-full md:w-1/2 relative items-center justify-center">
          <div className="relative w-[300px] lg:w-[400px] mt-16">
            <Image
              src="/images/commercants/inscription/iphone_mockup-merchant_card.svg"
              alt="iPhone mockup with merchant card"
              width={400}
              height={600}
              className="w-full h-auto"
              priority
            />
          </div>
        </div>

        {/* Right side - Form */}
        <div className="w-full md:w-1/2 p-4 md:p-8 flex flex-col justify-center">
          <div className="max-w-md mx-auto w-full">
            <div className="mb-4">
              <p className="text-[15px] text-[#5F6368] font-outfit">ÉTAPE 1 SUR 3</p>
            </div>

            <h1 className="font-lilita text-[#320606] text-2xl mb-3 uppercase">
              Fiche inscription de votre commerce
            </h1>

            <p className="text-[14px] text-[#5F6368] font-outfit mb-4">
              Les utilisateurs pourront consulter la page de votre commerce pour vous découvrir.
            </p>
            <div className="border-b border-gray-200 mb-6"></div>

            {/* Mobile phone mockup (visible only on mobile) */}
            <div className="md:hidden w-full flex justify-center mb-8">
              <div className="relative w-[240px]">
                <Image
                  src="/images/commercants/inscription/iphone_mockup-merchant_card.svg"
                  alt="iPhone mockup with merchant card"
                  width={240}
                  height={350}
                  className="w-full h-auto"
                  priority
                />
              </div>
            </div>

            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label htmlFor="name" className="block text-sm font-medium text-[#320606] mb-2 font-outfit">
                  Nom
                </label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Entrer le nom du commerce"
                  className="w-full px-4 py-3 h-12 rounded-md border-2 border-black focus:border-[#320606] focus:outline-none placeholder:text-gray-400"
                  required
                />
              </div>

              <div className="mb-6">
                <label htmlFor="category" className="block text-sm font-medium text-[#320606] mb-2 font-outfit">
                  Catégorie principale
                </label>
                <div className="relative">
                  <select
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                    className="w-full px-4 py-3 h-12 rounded-md border-2 border-black focus:border-[#320606] focus:outline-none appearance-none"
                    required
                  >
                    <option value="" disabled>Sélectionner une catégorie</option>
                    <option value="boulangerie">Boulangerie</option>
                    <option value="fromagerie">Fromagerie</option>
                    <option value="boucherie">Boucherie</option>
                    <option value="restaurant">Restaurant</option>
                    <option value="epicerie">Épicerie</option>
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-4">
                    <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <label htmlFor="phone" className="block text-sm font-medium text-[#320606] mb-2 font-outfit">
                  Numéro de téléphone
                </label>
                <div className="phone-input-container">
                  <PhoneInput
                    international
                    defaultCountry="FR"
                    value={phoneNumber}
                    onChange={(value) => value ? setPhoneNumber(value) : setPhoneNumber('')}
                    placeholder="Entrer le numéro du commerce"
                    required
                  />
                </div>
              </div>

              <div className="mb-8">
                <label htmlFor="email" className="block text-sm font-medium text-[#320606] mb-2 font-outfit">
                  Adresse mail
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="Entrer l'adresse mail du commerce"
                  className="w-full px-4 py-3 h-12 rounded-md border-2 border-black focus:border-[#320606] focus:outline-none placeholder:text-gray-400"
                  required
                />
              </div>

              <div className="flex justify-end">
                <button
                  type="submit"
                  className="bg-[#fee566] rounded-full px-10 py-2 hover:bg-[#fde04d] uppercase font-outfit text-[#320606] text-sm font-bold tracking-wide"
                >
                  Suivant
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
