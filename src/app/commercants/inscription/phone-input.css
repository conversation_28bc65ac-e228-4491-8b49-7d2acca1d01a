/* Custom styles for react-phone-number-input */

.phone-input-container {
  position: relative;
}

.phone-input-container .PhoneInput {
  display: flex;
  align-items: center;
  width: 100%;
  border: 2px solid black;
  border-radius: 0.375rem;
  overflow: hidden;
}

/* Flag and country select */
.phone-input-container .PhoneInputCountry {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

/* Country select button */
.phone-input-container .PhoneInputCountrySelect {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 1;
  border: 0;
  opacity: 0;
  cursor: pointer;
}

/* Country flag */
.phone-input-container .PhoneInputCountryIcon {
  width: 24px;
  height: 16px;
  margin-right: 8px;
}

/* Main input field */
.phone-input-container .PhoneInputInput {
  flex: 1;
  height: 48px;
  padding: 0.75rem 1rem;
  border: none;
  outline: none;
  font-size: 1rem;
  width: 100%;
}

/* Placeholder color */
.phone-input-container .PhoneInputInput::placeholder {
  color: #9CA3AF;
}

/* Remove default focus styles */
.phone-input-container .PhoneInputInput:focus {
  outline: none;
}

/* Country select container */
.phone-input-container .PhoneInputCountry {
  position: relative;
  align-self: stretch;
  display: flex;
  align-items: center;
  padding: 0 0.75rem;
  border-right: 1px solid #e5e7eb;
  background-color: white;
  min-width: 80px;
}

/* Focus state for the entire component */
.phone-input-container:focus-within {
  outline: none;
}

.phone-input-container:focus-within .PhoneInput {
  border-color: #320606;
}
