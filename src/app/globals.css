/* src/app/globals.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --font-poppins: 'Poppins', sans-serif;
  --font-inter: 'Inter', sans-serif;
}

body {
  font-family: var(--font-inter);
}

/* Apply the font to headings */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Saint Regus Bold', serif;
}

@font-face {
  font-family: 'Saint Regus Bold';
  src: url('/fonts/SaintRegus-ExtraBold.otf') format('opentype');
  font-weight: 800; /* Extra Bold */
  font-style: normal;
  font-display: swap;
}

/* Apply the font to headings */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Saint Regus Bold', serif;
}

/* Optional: Define a utility class for the custom font */
.font-saint-regus {
  font-family: 'Saint Regus Bold', serif;
}