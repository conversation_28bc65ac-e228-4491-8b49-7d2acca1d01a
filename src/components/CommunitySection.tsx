// src/components/CommunitySection.tsx
'use client';

import React from 'react';
import Image from 'next/image';

const CommunitySection: React.FC = () => {
  return (
    <section className="w-full py-16 px-4 sm:px-6 md:px-8 bg-[#320607]">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-4">
            UNE ADRESSE PÉPITE EN TÊTE ?
          </h2>
          <p className="text-lg sm:text-xl font-saint-regus text-white mb-8">
            Boucherie, boulangerie, café & bar, caviste, épicerie, fromagerie, marchés, primeur, restaurant, traiteur...
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-12 items-center">
          {/* Left side - Form */}
          <div className="flex justify-center">
            <div className="rounded-xl overflow-hidden bg-white shadow-lg max-w-[500px] w-full">
              <div className="bg-[#FFE569] px-6 py-6">
                <h3 className="text-2xl font-bold font-lilita text-center text-[#320607]">Recommander une pépite locale</h3>
              </div>
              <div className="p-6 bg-white">
                <form className="space-y-4">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Nom du commerçant"
                      className="w-full px-4 py-3 rounded-md border border-[#320607] focus:border-[#320607] focus:outline-none text-gray-600 placeholder-gray-400"
                    />
                  </div>

                  <div className="relative">
                    <select
                      className="w-full px-4 py-3 rounded-md border border-[#320607] focus:border-[#320607] focus:outline-none text-gray-400 appearance-none"
                      defaultValue=""
                    >
                      <option value="" disabled>Catégorie</option>
                      <option value="boulangerie">Boulangerie</option>
                      <option value="restaurant">Restaurant</option>
                      <option value="boucherie">Boucherie</option>
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-4">
                      <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>

                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Ville"
                      className="w-full px-4 py-3 rounded-md border border-[#320607] focus:border-[#320607] focus:outline-none text-gray-600 placeholder-gray-400"
                    />
                  </div>

                  <div className="flex justify-center mt-6">
                    <button
                      type="submit"
                      className="w-[50%] bg-[#320607] text-[#FFE569] py-3 rounded-full font-bold font-lilita hover:bg-opacity-90 transition-colors uppercase"
                    >
                      RECOMMANDER
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          {/* Right side - Image and Text */}
          <div className="text-center">
            {/* Placeholder for the 3 circular images */}
            <div className="mb-8">
              <Image
                src="/images/circular-images-placeholder.svg"
                alt="Community members"
                width={600}
                height={200}
                className="w-full"
              />
            </div>

            <p className="text-lg mb-4 text-white font-outfit">
              Rejoignez une communauté de gourmands et<br />
              régalez-vous chez vos commerçants indépendants !
            </p>

            <button className="bg-[#FFE569] text-[#2B0707] px-8 py-3 rounded-full font-bold hover:bg-opacity-90 transition-colors font-lilita">
              DÉCOUVRIR ET S&apos;INSCRIRE
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CommunitySection;