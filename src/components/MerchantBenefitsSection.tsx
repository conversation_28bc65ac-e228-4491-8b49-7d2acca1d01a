// src/components/MerchantBenefitsSection.tsx
'use client';

import React from 'react';
import Image from 'next/image';

const MerchantBenefitsSection: React.FC = () => {
  return (
    <section className="w-full my-20 py-16">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-3xl md:text-4xl font-black text-[#320606] mb-12">
          AU SERVICE DES COMMERÇANTS<br />
          ET DE LEUR RAYONNEMENT
        </h1>

        <div className="flex flex-col lg:flex-row gap-12">
          {/* Left Column - Text Content */}
          <div className="lg:w-1/2">
            <h2 className="text-2xl font-black text-[#320606] mb-8">
              REJOIGNEZ LE COLLECTIF D&apos;INDÉPENDANTS !
            </h2>

            <ul className="space-y-6">
              {[
                "Aucune friction dans le processus de vente.",
                "Le cashback distribué est uniquement dépensable chez les commerçants partenaires.",
                "Un programme de fidélité exclusif aux commerces de bouche.",
                "Pepit vous rapporte (augmentation du CA, panier moyen, fréquentation, nouveaux clients, etc).",
                "Accéder à des nouveaux outils et données de consommation."
              ].map((benefit, index) => (
                <li key={index} className="flex items-start gap-4">
                  <div className="w-8 h-8 mt-0.75 rounded-full bg-[#FFE569] flex-shrink-0" />
                  <span className="text-xl font-medium">{benefit}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Right Column - Image and Button */}
          <div className="lg:w-1/2">
            {/* Image placeholder - you can add the actual image path */}
            <Image
              src="/images/merchant-photo.svg" // Add your image path here
              alt="Merchant smiling in their shop"
              width={600}
              height={400}
              className="w-full rounded-2xl mb-8"
            />

            <div className="text-center">
              <button className="bg-[#FFE569] text-[#320606] px-12 py-4 rounded-full text-xl font-bold font-saint-regus hover:bg-opacity-90 transition-colors">
                DÉCOUVRIR ET S&apos;INSCRIRE
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MerchantBenefitsSection;