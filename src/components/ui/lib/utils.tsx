// src/components/ui/lib/utils.tsx
import { clsx, ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Combines class names using clsx and tailwind-merge.
 * Accepts any ClassValue, including undefined, null, etc.
 * @param inputs - Class names to be combined.
 * @returns A string of merged class names.
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(...inputs));
}
