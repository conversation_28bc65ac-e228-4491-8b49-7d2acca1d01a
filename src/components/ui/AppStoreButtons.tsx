'use client';

import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faApple, faGooglePlay } from '@fortawesome/free-brands-svg-icons';

const AppStoreButtons = () => {
  const styles = {
    appButtonContainer: {
      backgroundColor: '#FFE569',
      borderRadius: '9999px',
      padding: '11px 20px',
      display: 'flex',
      alignItems: 'center',
      gap: '12px',
    },
    appButton: {
      background: 'none',
      border: 'none',
      cursor: 'pointer',
      padding: '0',
    },
    icon: {
      color: '#4a2511',
      fontSize: '20px',
    },
  };

  return (
    <div style={styles.appButtonContainer}>
      <button style={styles.appButton} onClick={() => window.open('https://apps.apple.com', '_blank')}>
        <FontAwesomeIcon icon={faApple} style={styles.icon} />
      </button>
      <button style={styles.appButton} onClick={() => window.open('https://play.google.com', '_blank')}>
        <FontAwesomeIcon icon={faGooglePlay} style={styles.icon} />
      </button>
    </div>
  );
};

export default AppStoreButtons;