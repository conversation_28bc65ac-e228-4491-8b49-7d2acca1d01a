'use client';

import React, { useEffect } from 'react';

const PepitAppClient: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  useEffect(() => {
    // Load Google Fonts
    const link = document.createElement('link');
    link.href = 'https://fonts.googleapis.com/css2?family=Bebas+Neue&display=swap';
    link.rel = 'stylesheet';
    document.head.appendChild(link);
  }, []);

  return <>{children}</>;
};

export default PepitAppClient;