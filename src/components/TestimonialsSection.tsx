// src/components/TestimonialsSection.tsx
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';

// Define the SVG images for testimonials - just 2 files
const testimonialImages = [
  "/images/testimonial/testimonial-1.svg", // Path to your first testimonial SVG
  "/images/testimonial/testimonial-2.svg", // Path to your second testimonial SVG
];

const TestimonialsSection: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  
  // Function to navigate to next slide
  const nextSlide = useCallback(() => {
    setActiveIndex((prev) => (prev === testimonialImages.length - 1 ? 0 : prev + 1));
  }, []);

  // Function to navigate to previous slide
  const prevSlide = useCallback(() => {
    setActiveIndex((prev) => (prev === 0 ? testimonialImages.length - 1 : prev - 1));
  }, []);
  
  // Auto-rotation effect
  useEffect(() => {
    if (!isPaused) {
      const interval = setInterval(() => {
        nextSlide();
      }, 5000); // Rotate every 5 seconds
      
      return () => clearInterval(interval);
    }
  }, [isPaused, nextSlide]);
  
  return (
    <section className="w-full my-20 py-16">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-4xl md:text-5xl font-black text-center mb-16">
          MESSAGE A TOUS LES GOURMANDS :<br />
          ON VA VOUS FAIRE FONDRE DE PLAISIR !
        </h2>

        {/* Testimonials carousel */}
        <div className="max-w-6xl mx-auto mb-16 relative px-4 sm:px-8"
           onMouseEnter={() => setIsPaused(true)}
           onMouseLeave={() => setIsPaused(false)}>
        
          {/* Testimonial Images Carousel */}
          <div className="relative overflow-hidden">
            <div 
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${activeIndex * 100}%)` }}
            >
              {testimonialImages.map((src, index) => (
                <div key={index} className="min-w-full flex justify-center items-center">
                  <Image 
                    src={src} 
                    alt={`Testimonial ${index + 1}`} 
                    width={1000} 
                    height={200} 
                    className="w-full h-auto"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Previous button */}
          <button
            onClick={prevSlide}
            className="absolute left-0 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-800 transition-colors"
            aria-label="Previous testimonial"
          >
            <svg width="50" height="50" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
          
          {/* Next button */}
          <button
            onClick={nextSlide}
            className="absolute right-0 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-800 transition-colors"
            aria-label="Next testimonial"
          >
            <svg width="50" height="50" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>

          {/* Dots indicators - just 2 dots */}
          <div className="flex justify-center mt-4 gap-2">
            {testimonialImages.map((_, index) => (
              <button
                key={index}
                onClick={() => setActiveIndex(index)}
                aria-label={`Go to slide ${index + 1}`}
                className={`h-2 w-2 rounded-full transition-colors ${
                  activeIndex === index ? 'bg-[#330709]' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        </div>

        <div className="text-center">
          <h3 className="text-3xl font-bold mb-8">REJOINDRE LA COMMUNAUTÉ</h3>
          
          {/* Social Media Icons Grid */}
          <div className="flex justify-center gap-8">
            <Image src="/images/social/instagram.svg" alt="Instagram" width={48} height={48} className="squared-full" />
            <Image src="/images/social/tiktok.svg" alt="TikTok" width={48} height={48} className="squared-full" />
            <Image src="/images/social/facebook.svg" alt="Facebook" width={48} height={48} className="squared-full" />
            <Image src="/images/social/linkedin.svg" alt="LinkedIn" width={48} height={48} className="squared-full" />
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;