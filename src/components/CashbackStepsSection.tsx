// src/components/CashbackStepsSection.tsx
'use client';

import React from 'react';
import Image from 'next/image';

const CashbackStepsSection: React.FC = () => {
  return (
    <section className="w-full my-20 py-16">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-4xl md:text-5xl font-bold text-noirChaud text-center mb-16 font-saint-regus">
          LE CASHBACK INSTANTANÉ AVEC PEPIT
        </h2>

        {/* Security Banner */}
        <div className="flex items-center justify-center gap-4 mb-16">
          <Image
            src="/images/shield-icon.svg"
            alt="Security shield icon"
            width={64}
            height={64}
            className="rounded-full"
          />
          <p className="text-xl font-bold font-saint-regus text-noirChaud">
            Un service cashback 100% sécurisé<br />
            grâce à nos partenaires bancaires
          </p>
        </div>

        {/* Steps Container */}
        <div className="max-w-6xl mx-auto grid grid-cols-2 gap-12">
          {/* Left Column - Steps 1 & 2 */}
          <div className="space-y-12">
            {/* Step 1 */}
            <div className="flex gap-6">
              <div className="w-24 h-24 bg-[#ffe569] rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-4xl font-black font-saint-regus">01</span>
              </div>
              <div>
                <h3 className="text-3xl font-black mb-3">
                  Payer chez un<br />commerçant Pepit
                </h3>
                <p className="text-lg">
                  Ne changez rien, il vous suffit<br />seulement d&apos;utiliser votre CB
                </p>
              </div>
            </div>

            {/* Step 2 */}
            <div className="flex gap-6">
              <div className="w-24 h-24 bg-[#ffe569] rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-4xl font-black font-saint-regus">02</span>
              </div>
              <div>
                <h3 className="text-3xl font-black mb-3">
                  Récupérer du cashback<br />instantanément
                </h3>
                <p className="text-lg">
                  Recevez au moins 5% de votre<br />commande à chaque paiement
                </p>
              </div>
            </div>
          </div>

          {/* Right Column - Step 3 */}
          <div>
            <div className="flex gap-6">
              <div className="w-24 h-24 bg-[#ffe569] rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-4xl font-black font-saint-regus">03</span>
              </div>
              <div>
                <h3 className="text-3xl font-black mb-3">
                  Dépenser votre cagnotte chez n&apos;importe quel commerçant partenaire
                </h3>
                <div className="space-y-4">
                  <Image
                    src="/images/friends-eating.svg"
                    alt="Friends enjoying food"
                    width={600}
                    height={400}
                    className="rounded-lg w-full"
                  />
                  <p className="text-lg text-center">Faites-vous plaisir et régalez-vous !</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Download Button */}
        <div className="text-center mt-16">
          <div className="inline-block">
            <button className="bg-[#330709] text-[#ffe569] px-12 py-4 rounded-[50px] text-2xl font-black uppercase mb-2 hover:opacity-90 transition-opacity">
              Télécharger l&apos;application
            </button>
            <p className="text-gray-600 text-center mt-2">(très prochainement)</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CashbackStepsSection;