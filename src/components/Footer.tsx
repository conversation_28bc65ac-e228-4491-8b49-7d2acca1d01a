// src/components/Footer.tsx
'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

const Footer: React.FC = () => {
  return (
    <footer className="w-full bg-[#FEE566] py-12">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo Section */}
          <div className="flex flex-col items-start gap-4">
            <Image
              src="/images/footer/pepit-logo-marron.svg"
              alt="Pepit Logo"
              width={150}
              height={40}
              className="mb-4"
            />
            <div className="flex flex-col gap-2">
              <Image
                src="/images/footer/app-store.svg"
                alt="Download on App Store"
                width={150}
                height={44}
                className="h-auto"
              />
              <Image
                src="/images/footer/google-play.svg"
                alt="Get it on Google Play"
                width={150}
                height={44}
                className="h-auto"
              />
            </div>
          </div>

          {/* Utilisateur Section */}
          <div>
            <h3 className="font-bold text-xl text-noirChaud mb-4">Utilisateur</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-noirChaud hover:text-orangeSaumon">Fonctionnement</a></li>
            </ul>
            
            <h3 className="font-bold text-xl text-noirChaud mt-8 mb-4">Centre d&apos;aide</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/contact" className="text-noirChaud hover:text-orangeSaumon">
                  Nous contacter
                </Link>
              </li>
              <li><a href="#" className="text-noirChaud hover:text-orangeSaumon">FAQ</a></li>
            </ul>
          </div>

          {/* Commerçant Section */}
          <div>
            <h3 className="font-bold text-xl text-noirChaud mb-4">Commerçant</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/commercants" className="text-noirChaud hover:text-orangeSaumon">
                  Découvrir et s&apos;inscrire
                </Link>
              </li>
            </ul>

            <h3 className="font-bold text-xl text-noirChaud mt-8 mb-4">Mission</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-noirChaud hover:text-orangeSaumon">Notre mission</a></li>
              <li>
                <Link href="/charte-ethique" className="text-noirChaud hover:text-orangeSaumon">
                  Charte éthique
                </Link>
              </li>
            </ul>
          </div>

          {/* Informations légales Section */}
          <div>
            <h3 className="font-bold text-xl text-noirChaud mb-4">Informations légales</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-noirChaud hover:text-orangeSaumon">Politique de confidentialité</a></li>
              <li><a href="#" className="text-noirChaud hover:text-orangeSaumon">Conditions générales de service</a></li>
              <li><a href="#" className="text-noirChaud hover:text-orangeSaumon">Conditions générales d&apos;utilisation</a></li>
              <li><a href="#" className="text-noirChaud hover:text-orangeSaumon">Conditions générales de vente</a></li>
            </ul>
          </div>
        </div>

        {/* Delimiter */}
        <div className="max-w-4xl mx-auto">
          <div className="w-full h-[1px] bg-noirChaud opacity-20 my-8" />
        </div>

        {/* Copyright */}
        <div className="text-center">
          <p className="text-noirChaud">© 2025 Pepit. Tous droits réservés.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;