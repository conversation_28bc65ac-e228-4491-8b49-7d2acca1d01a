// src/components/HeroSection.tsx
'use client';

import React from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';

const HeroSection: React.FC = () => {
  return (
    <motion.section
      className="w-full text-center py-12 md:py-24 relative overflow-hidden bg-cover bg-center"
      style={{ backgroundImage: 'url(/images/hero-background.jpg)' }}
      initial={{ opacity: 0, y: -30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 1 }}
    >
      {/* Mobile/Tablet Layout (hidden on desktop) */}
      <div className="block lg:hidden">
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          {/* 1. Main heading text (appears first on mobile) */}
          <div className="mb-8">
            <h1
              className="text-2xl sm:text-3xl md:text-4xl font-extrabold mb-2 font-saint-regus drop-shadow-lg"
              style={{ color: '#330709' }}
            >
              L&apos;APP QUI FAIT TOURNER
            </h1>
            <h1
              className="text-2xl sm:text-3xl md:text-4xl font-bold mb-2 font-saint-regus drop-shadow-md"
              style={{ color: '#330709' }}
            >
              L&apos;ÉCONOMIE LOCALE
            </h1>
            <h1
              className="text-2xl sm:text-3xl md:text-4xl font-bold mb-6 font-saint-regus drop-shadow-md"
              style={{ color: '#330709' }}
            >
              ET VOUS RÉGALE !
            </h1>
          </div>

          {/* 2. Hero image (appears in middle on mobile) */}
          <motion.div
            className="flex justify-center mb-8"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1 }}
          >
            <Image
              src="/images/hero-image.svg"
              alt="Sauce Image"
              width={320}
              height={260}
              className="max-w-full h-auto"
              style={{ objectFit: 'contain', filter: 'drop-shadow(0 4px 6px rgba(0,0,0,0.1))' }}
            />
          </motion.div>

          {/* 3. Subtitle text (appears last on mobile) */}
          <motion.div
            className="px-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1, duration: 0.8 }}
          >
            {/* Main Description Text */}
            <p
              className="font-outfit text-center mb-6"
              style={{
                fontWeight: 600,
                fontSize: '18px',
                lineHeight: '130%',
                color: '#320606',
                maxWidth: '100%',
                letterSpacing: '-0.01em',
              }}
            >
              Avec l&apos;application de fidélité Pepit, recevez instantanément 5% de cashback chez vos commerçants indépendants. Plus besoin de carte de fidélité, utilisez seulement votre CB !
            </p>

            {/* Download Button */}
            <div className="mb-5 flex justify-center">
              <a href="#" target="_blank" rel="noopener noreferrer">
                <button
                  className="flex flex-row items-center justify-center font-saint-regus transition-all duration-300 hover:shadow-lg"
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: '12px 24px',
                    gap: '10px',
                    isolation: 'isolate',
                    width: 'auto',
                    minWidth: '240px',
                    height: '44px',
                    background: '#FFE569',
                    boxShadow: '0px 15px 25px rgba(0, 0, 0, 0.08)',
                    borderRadius: '50px',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '12px',
                    fontWeight: 'bold',
                    whiteSpace: 'nowrap',
                    letterSpacing: '0.02em',
                  }}
                >
                  TÉLÉCHARGER L&apos;APPLICATION
                </button>
              </a>
            </div>

            {/* Coming Soon Text */}
            <p
              className="font-outfit text-center"
              style={{
                fontWeight: 400,
                fontSize: '14px',
                lineHeight: '120%',
                color: '#320607',
                width: '100%',
                marginTop: '4px',
              }}
            >
              (très prochainement)
            </p>
          </motion.div>
        </div>
      </div>

      {/* Desktop Layout (hidden on mobile/tablet) */}
      <div className="hidden lg:block">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          {/* Headline */}
          <h1
            className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-extrabold mb-2 font-saint-regus drop-shadow-lg"
            style={{ color: '#330709' }}
          >
            L&apos;APP QUI FAIT TOURNER
          </h1>
          <h1
            className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-2 font-saint-regus drop-shadow-md"
            style={{ color: '#330709' }}
          >
            L&apos;ÉCONOMIE LOCALE
          </h1>
          <h1
            className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-6 font-saint-regus drop-shadow-md"
            style={{ color: '#330709' }}
          >
            ET VOUS RÉGALE !
          </h1>
        </div>
        <div className="relative w-full max-w-7xl mx-auto pt-12 sm:pt-16 pb-16 sm:pb-24">
          {/* Gastronomie Image */}
          <motion.div
            className="absolute bottom-[-45px] sm:bottom-[-85px] left-[5%] md:left-[8%] lg:left-[5%] z-10"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1 }}
          >
            <Image
              src="/images/hero-image.svg"
              alt="Sauce Image"
              width={520}
              height={420}
              style={{ objectFit: 'contain', filter: 'drop-shadow(0 4px 6px rgba(0,0,0,0.1))' }}
            />
          </motion.div>

          {/* Text Content Block - Positioned relative to the same container as the image */}
          <motion.div
            className="absolute z-20"
            style={{
              right: '9%', // Position relative to container, not viewport
              top: '20%', // Position relative to container
              width: '400px', // Slightly smaller width to match target proportions
              transform: 'translateY(-50%)', // Center vertically relative to position
              maxWidth: '400px',
            }}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 1, duration: 0.8 }}
          >
          {/* Main Description Text */}
          <p
            className="font-outfit text-left mb-6"
            style={{
              fontWeight: 600,
              fontSize: '20px', // Slightly larger to match target
              lineHeight: '120%', // Very tight line height for compact look
              color: '#320606',
              width: '100%',
              maxWidth: '400px', // Constrain width for better text wrapping
              letterSpacing: '-0.01em', // Slight letter spacing adjustment
            }}
          >
            Avec l&apos;application de fidélité Pepit, recevez instantanément 5% de cashback chez vos commerçants indépendants. Plus besoin de carte de fidélité, utilisez seulement votre CB !
          </p>

          {/* Download Button */}
          <div className="mb-5">
            <a href="#" target="_blank" rel="noopener noreferrer">
              <button
                className="flex flex-row items-center justify-center font-saint-regus transition-all duration-300 hover:shadow-lg"
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '14px 28px', // Slightly smaller padding to match target
                  gap: '10px',
                  isolation: 'isolate',
                  width: 'auto', // Auto width to fit content
                  minWidth: '260px', // Slightly smaller minimum width
                  height: '48px', // Slightly smaller height
                  background: '#FFE569',
                  boxShadow: '0px 15px 25px rgba(0, 0, 0, 0.08)', // Softer shadow
                  borderRadius: '50px',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '13px', // Slightly smaller font
                  fontWeight: 'bold',
                  whiteSpace: 'nowrap', // Prevent text wrapping
                  letterSpacing: '0.02em', // Slight letter spacing for button text
                }}
              >
                TÉLÉCHARGER L&apos;APPLICATION
              </button>
            </a>
          </div>

          {/* Coming Soon Text */}
          <p
            className="font-outfit text-center"
            style={{
              fontWeight: 400, // Slightly bolder to match target
              fontSize: '16px', // Fixed size to match target
              lineHeight: '120%',
              color: '#320607',
              width: '100%',
              maxWidth: '300px',
              marginTop: '4px', // Smaller gap to move text higher
            }}
          >
            (très prochainement)
          </p>
        </motion.div>
        </div>
      </div>
    </motion.section>
  );
};

export default HeroSection;