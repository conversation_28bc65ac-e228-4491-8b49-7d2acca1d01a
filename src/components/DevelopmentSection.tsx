// src/components/DevelopmentSection.tsx
'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Button } from "../components/ui/button";
import { FaFacebookF, FaLinkedinIn, FaInstagram } from 'react-icons/fa';
import { SiTiktok } from 'react-icons/si';

const DevelopmentSection: React.FC = () => {
  return (
    <section className="w-full my-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-5xl font-bold text-noirChaud text-center mb-16 font-saint-regus">
          L&apos;app qui va vous faire fondre !
        </h2>
        <motion.div
          className="flex flex-col items-center"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 1 }}
          viewport={{ once: true }}
        >
          <p className="text-xl text-noirChaud mb-12">
            Notre application est en cours de développement. Restez à l&apos;affût, ça va être croustillant !
          </p>
          <div className="flex justify-center space-x-6">
            {/* Instagram Button */}
            <a href="https://www.instagram.com/pepit-app" target="_blank" rel="noopener noreferrer">
              <Button
                variant="outline"
                size="icon"
                className="hover:bg-pink-500 hover:text-white transition-colors duration-300 border-2 border-pink-500 text-pink-500 rounded-full p-4"
                aria-label="Instagram"
              >
                <FaInstagram className="w-6 h-6" />
              </Button>
            </a>

            {/* TikTok Button */}
            <a href="https://www.tiktok.com/@pepit-app" target="_blank" rel="noopener noreferrer">
              <Button
                variant="outline"
                size="icon"
                className="hover:bg-black hover:text-white transition-colors duration-300 border-2 border-black text-black rounded-full p-4"
                aria-label="TikTok"
              >
                <SiTiktok className="w-6 h-6" />
              </Button>
            </a>

            {/* Facebook Button */}
            <a href="https://www.facebook.com/pepit-app" target="_blank" rel="noopener noreferrer">
              <Button
                variant="outline"
                size="icon"
                className="hover:bg-blue-600 hover:text-white transition-colors duration-300 border-2 border-blue-600 text-blue-600 rounded-full p-4"
                aria-label="Facebook"
              >
                <FaFacebookF className="w-6 h-6" />
              </Button>
            </a>

            {/* LinkedIn Button */}
            <a href="https://www.linkedin.com/in/pepit-app" target="_blank" rel="noopener noreferrer">
              <Button
                variant="outline"
                size="icon"
                className="hover:bg-blue-700 hover:text-white transition-colors duration-300 border-2 border-blue-700 text-blue-700 rounded-full p-4"
                aria-label="LinkedIn"
              >
                <FaLinkedinIn className="w-6 h-6" />
              </Button>
            </a>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default DevelopmentSection;