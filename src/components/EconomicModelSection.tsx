// src/components/EconomicModelSection.tsx
'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';

const EconomicModelSection: React.FC = () => {
  // State to track which image is currently displayed
  const [currentImage, setCurrentImage] = useState<'wheel' | 'flow'>('wheel');
  // State to track if animation is paused
  const [isAnimationPaused, setIsAnimationPaused] = useState(false);
  // Ref to store the timeout ID for auto-resume
  const resumeTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // Effect to alternate between images every 5 seconds if animation is not paused
  useEffect(() => {
    // Only set up the interval if animation is not paused
    if (!isAnimationPaused) {
      const intervalId = setInterval(() => {
        setCurrentImage(prev => prev === 'wheel' ? 'flow' : 'wheel');
      }, 5000); // 5 seconds interval

      // Clean up interval on component unmount or when animation is paused
      return () => clearInterval(intervalId);
    }
  }, [isAnimationPaused]); // Re-run effect when isAnimationPaused changes

  // Function to handle manual image change
  const handleImageChange = (image: 'wheel' | 'flow') => {
    setCurrentImage(image);
    setIsAnimationPaused(true); // Pause animation when user manually changes the image

    // Clear any existing timeout
    if (resumeTimeoutRef.current) {
      clearTimeout(resumeTimeoutRef.current);
    }

    // Set a new timeout to resume animation after 20 seconds
    resumeTimeoutRef.current = setTimeout(() => {
      setIsAnimationPaused(false); // Resume animation
    }, 20000); // 20 seconds
  };

  // Clean up timeout on component unmount
  useEffect(() => {
    return () => {
      if (resumeTimeoutRef.current) {
        clearTimeout(resumeTimeoutRef.current);
      }
    };
  }, []);
  return (
    <section className="w-full my-10 sm:my-16 md:my-20 bg-[#320607] py-10 sm:py-12 md:py-16">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row justify-between items-center gap-8 lg:gap-16">
          {/* Left Column */}
          <div className="w-full lg:w-1/2">
            <div className="mb-8 relative">
              <div className="flex items-start">
                <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-4 leading-tight font-saint-regus">
                  FAIRE TOURNER<br />
                  L&apos;ÉCONOMIE LOCALE
                </h2>
                <Image
                  src="/images/yellow-logo.svg"
                  alt="Pepit Logo"
                  width={48}
                  height={48}
                  className="ml-4 mt-12"
                />
              </div>

              <div className="flex flex-col gap-6 mt-8">
                <h3 className="text-xl sm:text-2xl font-bold text-white">
                  LA TRANSPARENCE ÇA N&apos;A PAS DE PRIX !
                </h3>

                <div className="flex flex-col sm:flex-row gap-4">
                  <button
                    className={`${currentImage === 'flow' ? 'bg-[#ffe569] text-[#320607]' : 'bg-transparent border-2 border-white text-white'} px-6 sm:px-8 py-3 flex items-center justify-center rounded-full text-sm sm:text-base font-bold font-saint-regus whitespace-nowrap h-[45px] sm:h-[50px] transition-colors duration-300`}
                    onClick={() => handleImageChange('flow')}
                  >
                    LE PARCOURS DU CASHBACK
                  </button>
                  <button
                    className={`${currentImage === 'wheel' ? 'bg-[#ffe569] text-[#320607]' : 'bg-transparent border-2 border-white text-white'} px-6 sm:px-8 py-3 flex items-center justify-center rounded-full text-sm sm:text-base font-bold font-saint-regus whitespace-nowrap h-[45px] sm:h-[50px] transition-colors duration-300`}
                    onClick={() => handleImageChange('wheel')}
                  >
                    MODÈLE ÉCONOMIQUE
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Economic Wheel */}
          <div className="w-full lg:w-1/2 relative">
            <div
              className="
                relative
                bg-white
                rounded-3xl sm:rounded-[40px] md:rounded-[50px] lg:rounded-[70px]
                p-4 sm:p-6 md:p-8
                w-full
                aspect-square sm:aspect-[5/4]
                shadow-lg
              "
            >
              <div className="flex flex-col items-center justify-center h-full">
                <div className="w-full h-full p-1 relative">
                  {/* Economic Wheel Image */}
                  <Image
                    src="/images/economic-model/economic-wheel.svg"
                    alt="Economic Model Wheel"
                    width={600}
                    height={600}
                    className={`w-full h-full object-contain absolute top-0 left-0 transition-opacity duration-1000 ${currentImage === 'wheel' ? 'opacity-100' : 'opacity-0'}`}
                    priority
                  />

                  {/* Cashback Flow Image */}
                  <Image
                    src="/images/economic-model/cashback-flow.svg"
                    alt="Cashback Flow Diagram"
                    width={600}
                    height={600}
                    className={`w-full h-full object-contain absolute top-0 left-0 transition-opacity duration-1000 ${currentImage === 'flow' ? 'opacity-100' : 'opacity-0'}`}
                    priority
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EconomicModelSection;