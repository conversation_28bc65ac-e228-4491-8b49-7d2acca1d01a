// src/components/Navigation.tsx
'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import AppStoreButtons from './ui/AppStoreButtons';

const Navigation: React.FC = () => {
  return (
    <nav className="fixed top-10 left-0 right-0 z-50 bg-transparent w-full">
      <div className="container mx-auto px-4 flex justify-between items-center">
        {/* Left side */}
        <div className="flex items-center gap-10">
          <Link 
            href="/notre-mission" 
            className="bg-[#FFE569] text-noirChaud px-6 py-3 rounded-full font-saint-regus font-bold"
          >
            NOTRE MISSION
          </Link>
          <Link 
            href="/fonctionnement" 
            className="bg-[#320606] text-[#FFE569] px-6 py-3 rounded-full font-saint-regus font-bold"
          >
            FONCTIONNEMENT
          </Link>
        </div>

        {/* Center - Logo */}
        <Link href="/" className="absolute left-1/2 transform -translate-x-1/2">
          <Image
            src="/images/LOGO-COMPLET-BICOLORE.svg"
            alt="Pepit Logo"
            width={150}
            height={40}
          />
        </Link>

        {/* Right side */}
        <div className="flex items-center gap-20">
          <Link 
            href="/commercants" 
            className="bg-[#FFE569] text-noirChaud px-6 py-3 rounded-full font-saint-regus font-bold"
          >
            COMMERÇANTS
          </Link>
          <AppStoreButtons />
        </div>
      </div>
    </nav>
  );
};

export default Navigation;