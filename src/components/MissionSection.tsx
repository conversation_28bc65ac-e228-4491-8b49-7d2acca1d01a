// src/components/MissionSection.tsx
'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

const MissionSection: React.FC = () => {
  return (
    <section className="w-full my-10 sm:my-16 md:my-20 bg-[#320607] py-10 sm:py-12 md:py-16">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-12">
        {/* Text Content */}
        <div className="w-full lg:w-1/2 order-2 lg:order-1">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-6 sm:mb-8 font-saint-regus leading-tight">
            Rendre la qualité de nos commerçants plus accessible
          </h2>
          <p className="text-base sm:text-lg md:text-xl font-bold text-white leading-relaxed font-serif mb-4 sm:mb-6">
            Pepit, c&apos;est la start-up bretonne qui travaille à bâtir une économie
            locale plus juste et plus durable en fédérant les commerçants.
          </p>
          <p className="text-base sm:text-lg md:text-xl font-bold text-white leading-relaxed font-serif mb-4 sm:mb-6">
            Utiliser Pepit c&apos;est découvrir les adresses où il fait bon manger et
            contribuer à la vie de votre quartier. Des commerçants de votre ville,
            aux artisans sur les marchés, jusqu&apos;aux restaurants, il y en a pour tous
            les goûts !
          </p>
          <p className="text-base sm:text-lg md:text-xl font-bold text-white leading-relaxed font-serif">
            Au service des commerçants indépendants et des utilisateurs, Pepit fait
            rayonner les uns et récompense les autres.
          </p>

          {/* Logos and Button Section */}
          <div className="flex flex-col sm:flex-row flex-wrap items-start sm:items-center gap-4 mt-6 sm:mt-8">
            {/* Pepit Logo */}
            <Image
              src="/images/mission/pepit-white.svg"
              alt="Pepit logo"
              width={100}
              height={35}
              className="h-auto"
            />

            <div className="flex flex-wrap items-center gap-3 sm:gap-4">
              {/* "une initiative soutenue par" Text */}
              <div className="bg-[#330709] px-3 sm:px-4 py-1.5 rounded-full">
                <span className="text-[#ffe569] text-xs sm:text-sm font-bold whitespace-nowrap">
                  une initiative soutenue par
                </span>
              </div>

              {/* Bretagne SVG */}
              <Image
                src="/images/mission/region-bretagne.svg"
                alt="Région Bretagne logo"
                width={50}
                height={32}
                className="h-auto w-auto max-h-8"
              />
            </div>

            {/* Charte Ethique Button */}
            <Link
              href="/charte-ethique"
              className="bg-[#ffe569] text-[#320607] hover:bg-opacity-90 transition-colors duration-300 px-4 sm:px-6 py-2 sm:py-3 rounded-full text-xs sm:text-sm font-bold whitespace-nowrap mt-4 sm:mt-0 inline-block text-center"
            >
              CONSULTER NOTRE<br /> CHARTE ÉTHIQUE
            </Link>
          </div>
        </div>

        {/* Image for iPhone Mockup */}
        <div className="w-full lg:w-1/2 order-1 lg:order-2 flex justify-center items-center">
          <Image
            src="/images/mission/iphone-mockup.svg"
            alt="iPhone mockup showing Pepit app"
            width={450}
            height={700}
            className="w-auto h-auto max-w-[280px] sm:max-w-[350px] md:max-w-[400px] lg:max-w-[450px]"
            priority
          />
        </div>
      </div>
    </section>
  );
};

export default MissionSection;