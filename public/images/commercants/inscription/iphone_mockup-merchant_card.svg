<svg width="615" height="811" viewBox="0 0 615 811" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_6_2)" filter="url(#filter0_d_6_2)">
<g filter="url(#filter1_i_6_2)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M156.19 12C120.187 12 91 38.5485 91 71.2978V735.702C91 768.452 120.187 795 156.19 795H455.474C491.478 795 520.664 768.452 520.664 735.702V259.972H522.146C523.783 259.972 525.109 258.766 525.109 257.277V215.499C525.109 214.01 523.783 212.804 522.146 212.804H520.664V199.327H522.146C523.783 199.327 525.109 198.121 525.109 196.632V154.854C525.109 153.365 523.783 152.158 522.146 152.158H520.664V71.2978C520.664 38.5485 491.478 12 455.474 12H156.19Z" fill="#EEEEEE"/>
</g>
<g filter="url(#filter2_i_6_2)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M227.307 44.3444C231.399 44.3444 234.715 41.3275 234.715 37.6061C234.715 33.8846 231.399 30.8677 227.307 30.8677C223.216 30.8677 219.899 33.8846 219.899 37.6061C219.899 41.3275 223.216 44.3444 227.307 44.3444ZM249.531 30.8677C245.441 30.8677 242.123 33.8846 242.123 37.6061C242.123 41.3275 245.441 44.3444 249.531 44.3444H382.875C386.966 44.3444 390.283 41.3275 390.283 37.6061C390.283 33.8846 386.966 30.8677 382.875 30.8677H249.531Z" fill="black" fill-opacity="0.02"/>
</g>
<g opacity="0.8">
<mask id="mask0_6_2" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="116" y="38" width="380" height="739">
<g opacity="0.8">
<path opacity="0.8" d="M495.477 38.9536H116.187V776.133H495.477V38.9536Z" fill="white"/>
<path opacity="0.8" fill-rule="evenodd" clip-rule="evenodd" d="M201.903 40.3013H160.635C136.905 40.3013 117.668 57.7992 117.668 79.3839V735.702C117.668 757.287 136.905 774.785 160.635 774.785H451.028C474.758 774.785 493.995 757.287 493.995 735.702V79.3839C493.995 57.7991 474.758 40.3013 451.028 40.3013H409.76C406.709 48.1528 398.478 53.778 388.801 53.778H222.862C213.186 53.778 204.954 48.1528 201.903 40.3013Z" fill="black"/>
</g>
</mask>
<g mask="url(#mask0_6_2)">
<path opacity="0.8" d="M201.903 40.3013L203.3 39.8522L202.951 38.9536H201.903V40.3013ZM409.76 40.3013V38.9536H408.713L408.364 39.8522L409.76 40.3013ZM160.635 41.649H201.903V38.9536H160.635V41.649ZM119.15 79.3839C119.15 58.5434 137.724 41.649 160.635 41.649V38.9536C136.087 38.9536 116.187 57.0549 116.187 79.3839H119.15ZM119.15 735.702V79.3839H116.187V735.702H119.15ZM160.635 773.437C137.724 773.437 119.15 756.543 119.15 735.702H116.187C116.187 758.032 136.087 776.133 160.635 776.133V773.437ZM451.029 773.437H160.635V776.133H451.029V773.437ZM492.513 735.702C492.513 756.543 473.94 773.437 451.029 773.437V776.133C475.577 776.133 495.477 758.032 495.477 735.702H492.513ZM492.513 79.3839V735.702H495.477V79.3839H492.513ZM451.029 41.649C473.94 41.649 492.513 58.5434 492.513 79.3839H495.477C495.477 57.0549 475.577 38.9536 451.029 38.9536V41.649ZM409.76 41.649H451.029V38.9536H409.76V41.649ZM408.364 39.8522C405.515 47.1821 397.83 52.4304 388.801 52.4304V55.1257C399.125 55.1257 407.904 49.1236 411.157 40.7503L408.364 39.8522ZM388.801 52.4304H222.862V55.1257H388.801V52.4304ZM222.862 52.4304C213.833 52.4304 206.148 47.1821 203.3 39.8522L200.506 40.7503C203.759 49.1236 212.538 55.1257 222.862 55.1257V52.4304Z" fill="#C7C7C7"/>
<g opacity="0.2">
<path opacity="0.2" d="M117.669 91.5132H91V92.8609H117.669V91.5132Z" fill="black"/>
<path opacity="0.2" d="M117.669 733.007H91V734.355H117.669V733.007Z" fill="black"/>
<path opacity="0.2" d="M520.664 91.5132H493.995V92.8609H520.664V91.5132Z" fill="black"/>
<path opacity="0.2" d="M520.664 733.007H493.995V734.355H520.664V733.007Z" fill="black"/>
</g>
</g>
</g>
<mask id="mask1_6_2" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="117" y="40" width="377" height="735">
<path fill-rule="evenodd" clip-rule="evenodd" d="M201.903 40.3013L160.635 40.3013C136.906 40.3013 117.669 57.7992 117.669 79.3839L117.669 735.702C117.669 757.287 136.906 774.785 160.635 774.785H451.029C474.758 774.785 493.995 757.287 493.995 735.702L493.995 79.3839C493.995 57.7991 474.758 40.3013 451.029 40.3013H409.76C406.71 48.1528 398.478 53.778 388.802 53.778L222.863 53.778C213.186 53.778 204.954 48.1528 201.903 40.3013Z" fill="#118BD6"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M201.903 40.3013L160.635 40.3013C136.906 40.3013 117.669 57.7992 117.669 79.3839L117.669 735.702C117.669 757.287 136.906 774.785 160.635 774.785H451.029C474.758 774.785 493.995 757.287 493.995 735.702L493.995 79.3839C493.995 57.7991 474.758 40.3013 451.029 40.3013H409.76C406.71 48.1528 398.478 53.778 388.802 53.778L222.863 53.778C213.186 53.778 204.954 48.1528 201.903 40.3013Z" fill="url(#paint0_linear_6_2)" fill-opacity="0.07"/>
</mask>
<g mask="url(#mask1_6_2)">
<rect x="117.936" y="39.564" width="375.422" height="735.039" fill="white"/>
</g>
</g>
<g filter="url(#filter3_d_6_2)">
<g clip-path="url(#clip1_6_2)">
<rect x="20" y="280" width="575" height="212" rx="20" fill="white"/>
<path d="M188.491 383.12C188.491 382.501 188.672 381.787 189.035 380.976C189.398 380.144 189.792 379.579 190.219 379.28C191.03 379.877 191.851 380.176 192.683 380.176L193.675 380.016C194.827 379.675 195.424 378.757 195.467 377.264V369.104H192.907C192.544 369.104 192.288 368.645 192.139 367.728C192.075 367.28 192.043 366.821 192.043 366.352C192.043 365.883 192.075 365.424 192.139 364.976C192.288 364.059 192.544 363.6 192.907 363.6H200.523C200.95 363.6 201.27 363.728 201.483 363.984C201.696 364.219 201.803 364.507 201.803 364.848V379.952C201.803 382.043 201.099 383.632 199.691 384.72C198.304 385.808 196.63 386.352 194.667 386.352C192.726 386.352 191.211 386.064 190.123 385.488C189.035 384.912 188.491 384.123 188.491 383.12ZM214.088 386.32C211.102 386.32 208.659 385.339 206.76 383.376C204.883 381.413 203.944 378.555 203.944 374.8C203.944 371.024 204.894 368.165 206.792 366.224C208.712 364.283 211.176 363.312 214.184 363.312C217.214 363.312 219.656 364.272 221.512 366.192C223.368 368.091 224.296 370.981 224.296 374.864C224.296 378.725 223.347 381.605 221.448 383.504C219.55 385.381 217.096 386.32 214.088 386.32ZM214.12 369.456C213.096 369.456 212.232 369.925 211.528 370.864C210.846 371.803 210.504 373.125 210.504 374.832C210.504 376.517 210.835 377.819 211.496 378.736C212.158 379.632 213.022 380.08 214.088 380.08C215.176 380.08 216.051 379.621 216.712 378.704C217.395 377.787 217.736 376.475 217.736 374.768C217.736 373.061 217.384 371.749 216.68 370.832C215.998 369.915 215.144 369.456 214.12 369.456ZM232.577 384.112V364.208C232.577 363.76 233.654 363.536 235.809 363.536C237.985 363.536 239.073 363.76 239.073 364.208V379.888H244.001C244.385 379.888 244.652 380.389 244.801 381.392C244.865 381.883 244.897 382.384 244.897 382.896C244.897 383.408 244.865 383.931 244.801 384.464C244.63 385.488 244.353 386 243.969 386H234.113C233.644 386 233.27 385.808 232.993 385.424C232.716 385.04 232.577 384.603 232.577 384.112ZM257.203 372.496C257.608 372.496 257.811 373.243 257.811 374.736C257.811 375.184 257.758 375.717 257.651 376.336C257.566 376.933 257.395 377.232 257.139 377.232H253.075V380.432H258.963C259.326 380.432 259.571 380.901 259.699 381.84C259.763 382.245 259.795 382.683 259.795 383.152C259.795 383.6 259.731 384.187 259.603 384.912C259.475 385.637 259.262 386 258.963 386H248.019C247.208 386 246.803 385.669 246.803 385.008V364.432C246.803 363.877 247.048 363.6 247.539 363.6H258.995C259.443 363.6 259.667 364.549 259.667 366.448C259.667 368.325 259.443 369.264 258.995 369.264H253.075V372.496H257.203ZM280.584 363.6C280.946 363.6 281.128 364.549 281.128 366.448C281.128 368.325 280.946 369.264 280.584 369.264H274.728V372.912H278.632C278.994 372.912 279.176 373.744 279.176 375.408C279.176 377.051 279.005 377.872 278.664 377.872H274.728V385.584C274.728 385.883 274.322 386.075 273.512 386.16C272.722 386.267 272.061 386.32 271.528 386.32L270.024 386.256C268.978 386.128 268.456 385.915 268.456 385.616V364.432C268.456 364.091 268.52 363.867 268.648 363.76C268.797 363.653 269.01 363.6 269.288 363.6H280.584ZM289.821 385.552C289.821 385.893 288.808 386.064 286.781 386.064C284.733 386.064 283.709 385.904 283.709 385.584V364.72C283.709 363.973 284.104 363.6 284.893 363.6H291.005C296.616 363.6 299.421 365.787 299.421 370.16C299.421 371.483 299.165 372.688 298.653 373.776C298.141 374.843 297.416 375.696 296.477 376.336L300.509 383.056C300.339 383.568 299.784 384.155 298.845 384.816C297.565 385.733 296.509 386.192 295.677 386.192C294.845 386.192 294.227 385.893 293.821 385.296L290.813 378.288H289.821V385.552ZM289.821 369.264V374.064H289.981C292.008 374.064 293.021 373.221 293.021 371.536C293.021 370.768 292.819 370.203 292.413 369.84C292.029 369.456 291.389 369.264 290.493 369.264H289.821ZM312.088 386.32C309.102 386.32 306.659 385.339 304.76 383.376C302.883 381.413 301.944 378.555 301.944 374.8C301.944 371.024 302.894 368.165 304.792 366.224C306.712 364.283 309.176 363.312 312.184 363.312C315.214 363.312 317.656 364.272 319.512 366.192C321.368 368.091 322.296 370.981 322.296 374.864C322.296 378.725 321.347 381.605 319.448 383.504C317.55 385.381 315.096 386.32 312.088 386.32ZM312.12 369.456C311.096 369.456 310.232 369.925 309.528 370.864C308.846 371.803 308.504 373.125 308.504 374.832C308.504 376.517 308.835 377.819 309.496 378.736C310.158 379.632 311.022 380.08 312.088 380.08C313.176 380.08 314.051 379.621 314.712 378.704C315.395 377.787 315.736 376.475 315.736 374.768C315.736 373.061 315.384 371.749 314.68 370.832C313.998 369.915 313.144 369.456 312.12 369.456ZM347.266 386.032C345.367 386.032 344.418 385.819 344.418 385.392L343.746 372.56L339.842 385.552C339.756 385.808 339.031 385.936 337.666 385.936C336.3 385.936 335.596 385.819 335.554 385.584L331.522 372.432L331.074 385.2C331.052 385.52 330.647 385.744 329.858 385.872C329.068 385.979 328.407 386.032 327.874 386.032C327.362 386.032 326.86 386 326.37 385.936C325.346 385.787 324.844 385.52 324.866 385.136L325.602 364.336C325.602 363.824 326.903 363.568 329.506 363.568C330.274 363.568 331.212 363.632 332.322 363.76C333.431 363.867 334.05 364.091 334.178 364.432L337.762 374.512L340.898 364.4C341.09 363.845 342.562 363.568 345.314 363.568C346.103 363.568 347.042 363.632 348.13 363.76C349.239 363.867 349.794 364.069 349.794 364.368L350.722 385.36C350.722 385.595 350.306 385.765 349.474 385.872C348.642 385.979 347.906 386.032 347.266 386.032ZM368.937 386.064C367.081 386.064 366.078 385.765 365.929 385.168L364.873 381.04H359.785L358.857 385.008C358.729 385.669 357.705 386 355.785 386C354.761 386 354.004 385.947 353.513 385.84C353.022 385.712 352.777 385.616 352.777 385.552L358.441 363.888C358.441 363.717 359.902 363.632 362.825 363.632C365.748 363.632 367.209 363.717 367.209 363.888L372.745 385.584C372.745 385.733 372.254 385.851 371.273 385.936C370.292 386.021 369.513 386.064 368.937 386.064ZM360.585 376.976H363.945L362.505 370.352H362.313L360.585 376.976ZM386.706 379.312L386.77 374.96C386.77 374.661 387.079 374.469 387.698 374.384C388.316 374.277 388.828 374.224 389.234 374.224L390.482 374.32C391.292 374.448 391.698 374.672 391.698 374.992L391.57 385.008C391.143 385.307 390.279 385.605 388.978 385.904C387.698 386.181 386.226 386.32 384.562 386.32C381.468 386.32 378.972 385.317 377.074 383.312C375.175 381.307 374.226 378.363 374.226 374.48C374.226 371.173 375.154 368.485 377.01 366.416C378.866 364.347 381.436 363.312 384.722 363.312C386.215 363.312 387.559 363.461 388.754 363.76C389.97 364.037 390.802 364.421 391.25 364.912C391.271 365.083 391.282 365.403 391.282 365.872C391.282 366.341 391.122 367.013 390.802 367.888C390.482 368.741 390.119 369.381 389.714 369.808C389.308 370.235 389.01 370.448 388.818 370.448C388.647 370.448 388.455 370.373 388.242 370.224C388.05 370.053 387.655 369.883 387.058 369.712C386.46 369.52 385.884 369.424 385.33 369.424C384.007 369.424 382.93 369.851 382.098 370.704C381.287 371.557 380.882 372.88 380.882 374.672C380.882 376.464 381.298 377.829 382.13 378.768C382.983 379.685 383.954 380.144 385.042 380.144C386.151 380.144 386.706 379.867 386.706 379.312ZM405.141 372.496C405.546 372.496 405.749 373.243 405.749 374.736C405.749 375.184 405.695 375.717 405.589 376.336C405.503 376.933 405.333 377.232 405.077 377.232H401.013V380.432H406.901C407.263 380.432 407.509 380.901 407.637 381.84C407.701 382.245 407.733 382.683 407.733 383.152C407.733 383.6 407.669 384.187 407.541 384.912C407.413 385.637 407.199 386 406.901 386H395.957C395.146 386 394.741 385.669 394.741 385.008V364.432C394.741 363.877 394.986 363.6 395.477 363.6H406.933C407.381 363.6 407.605 364.549 407.605 366.448C407.605 368.325 407.381 369.264 406.933 369.264H401.013V372.496H405.141ZM416.634 385.552C416.634 385.893 415.621 386.064 413.594 386.064C411.546 386.064 410.522 385.904 410.522 385.584V364.72C410.522 363.973 410.917 363.6 411.706 363.6H417.818C423.429 363.6 426.234 365.787 426.234 370.16C426.234 371.483 425.978 372.688 425.466 373.776C424.954 374.843 424.229 375.696 423.29 376.336L427.322 383.056C427.151 383.568 426.597 384.155 425.658 384.816C424.378 385.733 423.322 386.192 422.49 386.192C421.658 386.192 421.039 385.893 420.634 385.296L417.626 378.288H416.634V385.552ZM416.634 369.264V374.064H416.794C418.821 374.064 419.834 373.221 419.834 371.536C419.834 370.768 419.631 370.203 419.226 369.84C418.842 369.456 418.202 369.264 417.306 369.264H416.634Z" fill="#320606"/>
<g filter="url(#filter4_d_6_2)">
<rect x="223" y="406" width="169" height="36" rx="18" fill="#FE7953" shape-rendering="crispEdges"/>
<path d="M248.062 430V417.184H251.662V430H248.062ZM250.978 425.446V422.458H257.278V425.446H250.978ZM250.978 420.172V417.184H257.548V420.172H250.978ZM262.035 424.888V422.404H264.213C264.705 422.404 265.077 422.284 265.329 422.044C265.593 421.804 265.725 421.486 265.725 421.09C265.725 420.694 265.593 420.376 265.329 420.136C265.077 419.896 264.705 419.776 264.213 419.776H262.035V417.184H264.789C265.677 417.184 266.457 417.346 267.129 417.67C267.801 417.994 268.329 418.444 268.713 419.02C269.097 419.596 269.289 420.268 269.289 421.036C269.289 421.804 269.091 422.476 268.695 423.052C268.311 423.628 267.771 424.078 267.075 424.402C266.379 424.726 265.557 424.888 264.609 424.888H262.035ZM259.119 430V417.184H262.719V430H259.119ZM266.031 430L262.539 424.528L265.779 423.826L270.117 430H266.031ZM277.373 430.252C276.365 430.252 275.435 430.084 274.583 429.748C273.743 429.412 273.005 428.944 272.369 428.344C271.745 427.744 271.259 427.036 270.911 426.22C270.563 425.404 270.389 424.522 270.389 423.574C270.389 422.614 270.557 421.732 270.893 420.928C271.241 420.124 271.727 419.422 272.351 418.822C272.975 418.222 273.707 417.76 274.547 417.436C275.399 417.1 276.329 416.932 277.337 416.932C278.345 416.932 279.269 417.1 280.109 417.436C280.961 417.76 281.699 418.222 282.323 418.822C282.947 419.422 283.427 420.13 283.763 420.946C284.111 421.75 284.285 422.632 284.285 423.592C284.285 424.54 284.111 425.422 283.763 426.238C283.427 427.042 282.947 427.75 282.323 428.362C281.699 428.962 280.967 429.43 280.127 429.766C279.287 430.09 278.369 430.252 277.373 430.252ZM277.337 427.084C278.009 427.084 278.591 426.94 279.083 426.652C279.575 426.364 279.953 425.956 280.217 425.428C280.481 424.9 280.613 424.282 280.613 423.574C280.613 423.046 280.535 422.572 280.379 422.152C280.235 421.72 280.019 421.354 279.731 421.054C279.443 420.742 279.095 420.508 278.687 420.352C278.291 420.184 277.841 420.1 277.337 420.1C276.665 420.1 276.083 420.244 275.591 420.532C275.099 420.808 274.721 421.21 274.457 421.738C274.193 422.254 274.061 422.866 274.061 423.574C274.061 424.114 274.133 424.6 274.277 425.032C274.433 425.464 274.655 425.836 274.943 426.148C275.231 426.448 275.573 426.682 275.969 426.85C276.377 427.006 276.833 427.084 277.337 427.084ZM285.767 430V417.184H288.377L293.147 424.168H292.013L296.765 417.184H299.375V430H295.811V423.088L296.333 423.232L293.723 426.976H291.419L288.827 423.232L289.331 423.088V430H285.767ZM300.546 430L305.424 417.184H309.096L313.92 430H310.176L306.594 419.11H307.89L304.218 430H300.546ZM303.714 427.84V425.032H310.878V427.84H303.714ZM320.65 430.216C319.654 430.216 318.73 430.054 317.878 429.73C317.038 429.394 316.3 428.926 315.664 428.326C315.04 427.726 314.554 427.024 314.206 426.22C313.858 425.416 313.684 424.54 313.684 423.592C313.684 422.632 313.864 421.75 314.224 420.946C314.596 420.142 315.106 419.446 315.754 418.858C316.414 418.258 317.176 417.796 318.04 417.472C318.904 417.136 319.828 416.968 320.812 416.968C322 416.968 323.062 417.172 323.998 417.58C324.946 417.988 325.714 418.54 326.302 419.236L323.872 421.666C323.428 421.15 322.966 420.766 322.486 420.514C322.006 420.262 321.43 420.136 320.758 420.136C320.122 420.136 319.546 420.28 319.03 420.568C318.514 420.844 318.1 421.24 317.788 421.756C317.488 422.272 317.338 422.878 317.338 423.574C317.338 424.27 317.476 424.882 317.752 425.41C318.04 425.926 318.436 426.328 318.94 426.616C319.444 426.904 320.032 427.048 320.704 427.048C321.352 427.048 321.898 426.922 322.342 426.67C322.786 426.418 323.122 426.058 323.35 425.59C323.59 425.122 323.71 424.57 323.71 423.934L326.176 425.5L320.218 425.32V422.44H327.328V422.98C327.328 424.516 327.046 425.824 326.482 426.904C325.93 427.984 325.15 428.806 324.142 429.37C323.146 429.934 321.982 430.216 320.65 430.216ZM328.675 430V417.184H332.275V430H328.675ZM331.591 430V427.012H338.467V430H331.591ZM331.591 424.924V422.008H337.801V424.924H331.591ZM331.591 420.172V417.184H338.359V420.172H331.591ZM343.087 424.888V422.404H345.265C345.757 422.404 346.129 422.284 346.381 422.044C346.645 421.804 346.777 421.486 346.777 421.09C346.777 420.694 346.645 420.376 346.381 420.136C346.129 419.896 345.757 419.776 345.265 419.776H343.087V417.184H345.841C346.729 417.184 347.509 417.346 348.181 417.67C348.853 417.994 349.381 418.444 349.765 419.02C350.149 419.596 350.341 420.268 350.341 421.036C350.341 421.804 350.143 422.476 349.747 423.052C349.363 423.628 348.823 424.078 348.127 424.402C347.431 424.726 346.609 424.888 345.661 424.888H343.087ZM340.171 430V417.184H343.771V430H340.171ZM347.083 430L343.591 424.528L346.831 423.826L351.169 430H347.083ZM352.072 430V417.184H355.672V430H352.072ZM357.802 430V417.184H361.402V430H357.802ZM360.718 430V427.012H367.594V430H360.718ZM360.718 424.924V422.008H366.928V424.924H360.718ZM360.718 420.172V417.184H367.486V420.172H360.718Z" fill="white"/>
</g>
</g>
<rect x="22" y="282" width="571" height="208" rx="18" stroke="#320606" stroke-width="4"/>
</g>
<g filter="url(#filter5_d_6_2)">
<path d="M242.7 275.186C242.7 250.487 263.334 230.465 305.5 230.465C347.666 230.465 368.3 250.487 368.3 275.186C368.3 279.115 365.433 282.3 361.897 282.3H249.103C245.567 282.3 242.7 279.115 242.7 275.186Z" fill="#320606"/>
<path d="M329.05 180.25C329.05 193.256 318.506 203.8 305.5 203.8C292.494 203.8 281.95 193.256 281.95 180.25C281.95 167.244 292.494 156.7 305.5 156.7C318.506 156.7 329.05 167.244 329.05 180.25Z" fill="#320606"/>
</g>
<g filter="url(#filter6_d_6_2)">
<rect x="133" y="517" width="350" height="107" rx="20" fill="#E7E7E7"/>
<rect x="133.5" y="517.5" width="349" height="106" rx="19.5" stroke="#F8F9FA"/>
</g>
<g filter="url(#filter7_d_6_2)">
<rect x="133" y="636" width="350" height="107" rx="20" fill="#E7E7E7"/>
<rect x="133.5" y="636.5" width="349" height="106" rx="19.5" stroke="#F8F9FA"/>
</g>
<defs>
<filter id="filter0_d_6_2" x="88" y="0" width="462.109" height="811" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="11" dy="2"/>
<feGaussianBlur stdDeviation="7"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6_2" result="shape"/>
</filter>
<filter id="filter1_i_6_2" x="89" y="11" width="436.109" height="784" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="-1"/>
<feGaussianBlur stdDeviation="4.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_6_2"/>
</filter>
<filter id="filter2_i_6_2" x="219.899" y="30.8677" width="170.384" height="14.4766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_6_2"/>
</filter>
<filter id="filter3_d_6_2" x="0" y="264" width="615" height="252" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="10"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.93198 0 0 0 0 0.93198 0 0 0 0 0.93198 0 0 0 0.501967 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6_2" result="shape"/>
</filter>
<filter id="filter4_d_6_2" x="188" y="391" width="239" height="106" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="20"/>
<feGaussianBlur stdDeviation="17.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6_2" result="shape"/>
</filter>
<filter id="filter5_d_6_2" x="223" y="141" width="165" height="165" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6_2" result="shape"/>
</filter>
<filter id="filter6_d_6_2" x="113" y="501" width="390" height="147" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="10"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.93198 0 0 0 0 0.93198 0 0 0 0 0.93198 0 0 0 0.501967 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6_2" result="shape"/>
</filter>
<filter id="filter7_d_6_2" x="113" y="620" width="390" height="147" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="10"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.93198 0 0 0 0 0.93198 0 0 0 0 0.93198 0 0 0 0.501967 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6_2" result="shape"/>
</filter>
<linearGradient id="paint0_linear_6_2" x1="381.593" y1="262.78" x2="32.426" y2="643.102" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="0.461814" stop-color="white" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_6_2">
<rect width="434.109" height="783" fill="white" transform="translate(91 12)"/>
</clipPath>
<clipPath id="clip1_6_2">
<rect x="20" y="280" width="575" height="212" rx="20" fill="white"/>
</clipPath>
</defs>
</svg>
