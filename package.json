{"name": "my-pepit-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@shadcn/ui": "^0.0.4", "@tailwindcss/forms": "^0.5.9", "class-variance-authority": "^0.7.0", "framer-motion": "^11.5.4", "lucide-react": "^0.441.0", "next": "^15.5.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-phone-number-input": "^3.4.12", "tailwind-merge": "^2.5.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.20", "eslint": "^8", "eslint-config-next": "14.2.11", "postcss": "^8.4.47", "shadcn-ui": "^0.2.3", "tailwindcss": "^3.4.11", "typescript": "^5"}}