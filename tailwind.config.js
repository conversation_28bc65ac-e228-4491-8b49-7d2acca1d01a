/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/**/*.{js,ts,jsx,tsx}',        // Include all files in src
    './app/**/*.{js,ts,jsx,tsx}',        // Include all files in app (Next.js 13+)
    // Remove './components/**/*.{js,ts,jsx,tsx}' and './pages/**/*.{js,ts,jsx,tsx}' if they are inside src/
  ],
  theme: {
    extend: {
      colors: {
        jeunPepit: '#ffe569',      // Bright yellow
        noirChaud: '#330709',      // Dark brown
        beige: '#fffdf3',          // Beige
        orangeSaumon: '#ff7a54',   // Salmon Orange
        vertLegumes: '#8bcd84',    // Vegetable Green
        bleuPoisson: '#93d4f2',    // Fish Blue
        roseCochon: '#f9b9be',     // Pig Pink
        brown600: '#4B3832',       // Custom brown for hover states
      },
      fontFamily: {
        'saint-regus': ['"Saint Regus Bold"', 'serif'], // Custom font
        serif: ['"Playfair Display"', 'serif'],          // Existing serif font
        lilita: ['var(--font-lilita-one)'],
        outfit: ['var(--font-outfit)'],
      },
      boxShadow: {
        'img-draopshadow': '0px 4px 10px rgba(0, 0, 0, 0.1)',
        'onboarding-icon-dropshadow': '0px 1px 2px rgba(0, 0, 0, 0.1)',
      }
    },
  },
  plugins: [],
};
